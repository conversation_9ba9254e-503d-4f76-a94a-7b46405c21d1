/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #000000;
    --primary-light: #1a1a1a;
    --secondary-color: #333333;
    --accent-color: #dc2626;
    --accent-light: #ef4444;
    --accent-dark: #991b1b;
    --success-color: #dc2626;
    --danger-color: #dc2626;
    --warning-color: #dc2626;
    --dark-color: #000000;
    --light-color: #0a0a0a;
    --gray-50: #0a0a0a;
    --gray-100: #1a1a1a;
    --gray-200: #262626;
    --gray-300: #404040;
    --gray-400: #525252;
    --gray-500: #737373;
    --gray-600: #a3a3a3;
    --gray-700: #d4d4d4;
    --gray-800: #e5e5e5;
    --gray-900: #f5f5f5;
    --white: #ffffff;
    --shadow-sm: 0 1px 3px rgba(220, 38, 38, 0.1);
    --shadow: 0 2px 8px rgba(220, 38, 38, 0.15);
    --shadow-md: 0 4px 12px rgba(220, 38, 38, 0.2);
    --shadow-lg: 0 8px 24px rgba(220, 38, 38, 0.25);
    --shadow-xl: 0 16px 40px rgba(220, 38, 38, 0.3);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --transition: all 0.2s ease-out;
    --transition-smooth: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--primary-color);
    overflow-x: hidden;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    cursor: none;
}

/* Custom Cursor */
.custom-cursor {
    position: fixed;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    background: var(--accent-color);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.1s ease-out;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.5);
}

.custom-cursor::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border: 1px solid var(--accent-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0.4;
}

.custom-cursor.hover {
    transform: translate(-50%, -50%) scale(1.5);
    background: var(--accent-light);
    box-shadow: 0 0 30px rgba(239, 68, 68, 0.7);
}

.custom-cursor.hover::before {
    width: 60px;
    height: 60px;
    opacity: 0.6;
    border-color: var(--accent-light);
}

.custom-cursor.click {
    transform: translate(-50%, -50%) scale(0.8);
}

.custom-cursor.text {
    transform: translate(-50%, -50%) scale(0.5);
    background: var(--gray-600);
}

.custom-cursor.text::before {
    width: 30px;
    height: 30px;
    border-color: var(--gray-600);
}

/* Cursor animations */
@keyframes cursorPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.8;
    }
}

.custom-cursor.loading {
    animation: cursorPulse 1s ease-in-out infinite;
    background: var(--accent-light);
}

.custom-cursor.loading::before {
    border-color: var(--accent-light);
    animation: spin 1s linear infinite;
}

/* Special cursor states for different elements */
.hero .custom-cursor {
    mix-blend-mode: difference;
}

.hero .custom-cursor.hover {
    background: var(--white);
    mix-blend-mode: difference;
}

.hero .custom-cursor.hover::before {
    border-color: var(--white);
}

/* Smooth cursor transitions */
.custom-cursor {
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
    contain: layout style paint;
}

/* Performance optimizations */
.custom-cursor,
.custom-cursor::before,
.custom-cursor::after {
    transform-origin: center;
    will-change: transform, opacity, background-color;
}

/* Hide default cursor on all interactive elements */
*, *::before, *::after {
    cursor: none !important;
}

/* Cursor trail effect */
.custom-cursor::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background: var(--accent-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.2s ease-out;
    opacity: 0.6;
    z-index: -1;
}

.custom-cursor.hover::after {
    width: 12px;
    height: 12px;
    background: var(--accent-light);
}

.custom-cursor.click::after {
    width: 4px;
    height: 4px;
}

/* Magnetic effect for buttons */
.btn, button {
    transition: transform 0.2s ease-out;
}

/* Ripple effect on click */
@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

.custom-cursor.ripple::after {
    animation: ripple 0.6s ease-out;
    background: rgba(220, 38, 38, 0.4);
    border-radius: 50%;
}

html {
    scroll-behavior: smooth;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 1000;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-sm);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--accent-color);
}

.nav-brand i {
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--accent-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--accent-color);
    border-radius: 1px;
}

.nav-auth {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    object-fit: cover;
}

/* Cursor interactions */
a, button, .btn, input, textarea, select, [role="button"] {
    cursor: none;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-decoration: none;
    cursor: none;
    transition: var(--transition);
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.btn-primary {
    background: var(--accent-color);
    color: var(--white);
    border: 1px solid var(--accent-color);
}

.btn-primary:hover {
    background: var(--accent-light);
    border-color: var(--accent-light);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    transition: var(--transition-smooth);
}

.btn-secondary {
    background: var(--gray-200);
    color: var(--gray-700);
}

.btn-secondary:hover {
    background: var(--gray-300);
}

.btn-outline {
    background: transparent;
    color: var(--accent-color);
    border: 2px solid var(--accent-color);
}

.btn-outline:hover {
    background: var(--accent-color);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-large {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--primary-color);
    color: var(--gray-800);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(220,38,38,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 1;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 600;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    letter-spacing: -0.02em;
}

.gradient-text {
    color: var(--accent-color);
    position: relative;
    display: inline-block;
}

.hero-description {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: var(--gray-700);
    line-height: 1.6;
    font-weight: 400;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Security Animation */
.security-animation {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.shield-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.shield-icon {
    font-size: 6rem;
    color: var(--accent-color);
    z-index: 2;
    position: relative;
    animation: float 6s ease-in-out infinite;
    filter: drop-shadow(0 4px 12px rgba(220, 38, 38, 0.4));
}

.pulse-ring {
    position: absolute;
    border: 1px solid rgba(220, 38, 38, 0.2);
    border-radius: 50%;
    width: 150px;
    height: 150px;
    animation: pulse 4s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
}

.pulse-ring.delay-1 {
    animation-delay: 1.5s;
    width: 200px;
    height: 200px;
    border-color: rgba(220, 38, 38, 0.15);
}

.pulse-ring.delay-2 {
    animation-delay: 3s;
    width: 250px;
    height: 250px;
    border-color: rgba(220, 38, 38, 0.1);
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-12px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.3;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

@keyframes elegantGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(74, 144, 226, 0.15);
    }
    50% {
        box-shadow: 0 0 30px rgba(74, 144, 226, 0.25);
    }
}

@keyframes statsPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes statsSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes numberCount {
    from {
        opacity: 0.5;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes gentleGlow {
    0%, 100% {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    50% {
        box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15);
    }
}

/* Sections */
.section {
    padding: 6rem 0;
    position: relative;
}

.section:nth-child(even) {
    background: var(--gray-100);
}

.bg-light {
    background-color: var(--light-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--gray-800);
}

.section-description {
    font-size: 1.125rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Search Section */
.search-container {
    background: var(--gray-100);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-bottom: 3rem;
    border: 1px solid var(--gray-200);
}

.search-tabs {
    display: flex;
    background: var(--gray-200);
}

.tab-btn {
    flex: 1;
    padding: 1rem 2rem;
    border: none;
    background: transparent;
    color: var(--gray-600);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.tab-btn.active {
    background: var(--gray-100);
    color: var(--accent-color);
    box-shadow: var(--shadow-sm);
}

.tab-content {
    padding: 2rem;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: elegantFadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.search-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-group {
    display: flex;
    gap: 1rem;
}

.form-input,
.form-textarea,
.form-select {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
}

.advanced-form {
    display: grid;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--gray-700);
}

/* Search Results */
.search-results {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.results-count {
    color: var(--gray-600);
    font-size: 0.875rem;
}

.results-list {
    padding: 1rem;
}

.result-item {
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    transition: var(--transition);
}

.result-item:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    transition: var(--transition-smooth);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.result-hash {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.875rem;
    color: var(--gray-600);
    background: var(--gray-100);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.verdict-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.verdict-safe {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.verdict-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.verdict-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

/* Statistics */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 4rem;
}

.stat-card {
    background: #ffffff;
    padding: 2rem 1.5rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(226, 232, 240, 0.8);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 16px 16px 0 0;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: rgba(59, 130, 246, 0.3);
}

.stat-card:hover .stat-icon {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.stat-card:hover .stat-number {
    color: var(--primary-color);
    transform: scale(1.02);
}

.stat-card.loading .stat-number {
    animation: statsPulse 1.5s ease-in-out infinite;
}

.stat-card.updating .stat-number {
    animation: numberCount 0.6s ease-out;
}

.stat-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.stat-icon.safe {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-icon.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.stat-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--gray-900);
    line-height: 1;
    transition: all 0.3s ease;
}

.stat-label {
    color: var(--gray-600);
    font-size: 0.95rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.stats-section {
    background: #ffffff;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 20px 20px 0 0;
}

.stats-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: rgba(59, 130, 246, 0.3);
}

.stats-section h3 {
    margin-bottom: 2rem;
    color: var(--gray-900);
    font-size: 1.25rem;
    font-weight: 700;
    text-align: center;
    position: relative;
}

.stats-section h3::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 1px;
}

.stats-result {
    margin-top: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    transition: all 0.3s ease;
}

.stats-result:hover {
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border-color: rgba(59, 130, 246, 0.2);
}

/* Enhanced Stats Result Styles */
.stats-result-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.stats-result-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stats-result-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.5);
    transition: all 0.3s ease;
}

.stats-result-item:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-result-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    font-size: 1rem;
    flex-shrink: 0;
}

.stats-result-icon.safe {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stats-result-icon.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.stats-result-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.stats-result-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    line-height: 1;
}

.stats-result-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.stats-result-section {
    margin-top: 1rem;
}

.stats-result-section h5 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stats-result-section h5 i {
    color: var(--primary-color);
}

.stats-result-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stats-result-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 8px;
    border: 1px solid rgba(226, 232, 240, 0.3);
    transition: all 0.3s ease;
}

.stats-result-list-item:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateX(4px);
}

.channel-name,
.plugin-name {
    font-weight: 500;
    color: var(--gray-800);
}

.channel-count {
    font-size: 0.875rem;
    color: var(--gray-600);
    background: rgba(59, 130, 246, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

.plugin-status {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

.plugin-status.safe {
    color: #059669;
    background: rgba(16, 185, 129, 0.1);
}

.plugin-status.danger {
    color: #dc2626;
    background: rgba(239, 68, 68, 0.1);
}

.stats-result-raw {
    margin-top: 1rem;
}

.stats-result-raw h5 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stats-result-raw pre {
    background: rgba(31, 41, 55, 0.05);
    padding: 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    color: var(--gray-700);
    overflow-x: auto;
    border: 1px solid rgba(226, 232, 240, 0.5);
}

/* Scan Section */
.scan-container {
    max-width: 600px;
    margin: 0 auto;
}

/* Enhanced Scan Result Styles */
.scan-result-header {
    text-align: center;
    margin-bottom: 2rem;
}

.scan-result-header h3 {
    color: var(--gray-800);
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.scan-result-header i {
    color: var(--primary-color);
}

.file-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.file-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    font-size: 1.25rem;
    flex-shrink: 0;
}

.file-icon.plugin {
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

.file-icon.python {
    background: linear-gradient(135deg, #3776ab, #ffd43b);
}

.file-icon.java {
    background: linear-gradient(135deg, #ed8b00, #f89820);
}

.file-icon.archive {
    background: linear-gradient(135deg, #6b7280, #9ca3af);
}

.file-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.file-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 1.1rem;
}

.file-type {
    color: var(--gray-600);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.result-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.result-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.result-field label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.result-field span {
    color: var(--gray-900);
    font-size: 0.95rem;
}

.hash-value {
    font-family: 'Courier New', monospace;
    background: var(--gray-100);
    padding: 0.5rem;
    border-radius: 6px;
    word-break: break-all;
    font-size: 0.8rem !important;
}

.status-completed {
    color: var(--success-color);
    font-weight: 600;
}

.status-processing {
    color: var(--warning-color);
    font-weight: 600;
}

.status-failed {
    color: var(--danger-color);
    font-weight: 600;
}

.result-description {
    margin-top: 1.5rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.result-description label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 0.5rem;
}

.result-description p {
    color: var(--gray-800);
    line-height: 1.6;
    margin: 0;
}

.threats-section {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(239, 68, 68, 0.05);
    border-radius: 8px;
    border-left: 4px solid var(--danger-color);
}

.threats-section label {
    font-weight: 600;
    color: var(--danger-color);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 1rem;
}

.threats-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.threat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--danger-color);
    font-weight: 500;
    padding: 0.5rem;
    background: rgba(239, 68, 68, 0.1);
    border-radius: 6px;
}

.threat-item i {
    flex-shrink: 0;
}

.upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius-lg);
    padding: 3rem 2rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--accent-color);
    background: rgba(220, 38, 38, 0.05);
    transform: translateY(-2px);
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-md);
}

.upload-icon {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: 1rem;
}

.upload-text h3 {
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

.upload-text p {
    color: var(--gray-500);
    margin-bottom: 1.5rem;
}

/* File Preview Styles */
.file-preview {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 12px;
    border: 2px solid var(--primary-color);
    margin-bottom: 1rem;
}

.file-preview-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    font-size: 1.25rem;
    flex-shrink: 0;
}

.file-preview-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    min-width: 0;
}

.file-preview-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 1rem;
    word-break: break-word;
}

.file-preview-details {
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* Enhanced Upload Area States */
.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.upload-area.dragover .upload-icon {
    color: var(--primary-color);
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* File Type Specific Icons */
.file-icon.fa-puzzle-piece,
.file-preview-icon .fa-puzzle-piece {
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

.file-icon.fa-python,
.file-preview-icon .fa-python {
    background: linear-gradient(135deg, #3776ab, #ffd43b);
}

.file-icon.fa-file-question,
.file-preview-icon .fa-file-question {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.scan-progress {
    margin-top: 2rem;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: var(--accent-color);
    width: 0%;
    transition: width 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}



.progress-text {
    color: var(--gray-600);
    font-weight: 500;
}

.scan-result {
    margin-top: 2rem;
    padding: 2rem;
    background: var(--gray-100);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
}

/* Footer */
.footer {
    background: var(--gray-900);
    color: var(--white);
    padding: 2rem 0;
    text-align: center;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.25rem;
}

.footer-text p {
    margin: 0.25rem 0;
    color: var(--gray-400);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: var(--white);
}

.spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 5rem;
    right: 1rem;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toast {
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    color: var(--white);
    font-weight: 500;
    box-shadow: var(--shadow-lg);
    animation: slideIn 0.3s ease-out;
    max-width: 400px;
}

.toast.success {
    background: var(--success-color);
}

.toast.error {
    background: var(--danger-color);
}

.toast.warning {
    background: var(--warning-color);
}

.toast.info {
    background: var(--primary-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Additional Animations */
@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes elegantSlideUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes elegantFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Hover Effects */
.btn:hover {
    transform: translateY(-1px);
    transition: var(--transition-smooth);
}

.btn:active {
    transform: translateY(0);
    transition: var(--transition);
}

/* Improved spacing and typography */
.hero-content {
    animation: elegantFadeIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

.hero-title {
    animation: elegantSlideUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    animation-delay: 0.2s;
}

.hero-description {
    animation: elegantSlideUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    animation-delay: 0.4s;
}

.hero-actions {
    animation: elegantSlideUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    animation-delay: 0.6s;
}

.security-animation {
    animation: elegantFadeIn 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    animation-delay: 0.8s;
}

.stat-card {
    animation: elegantSlideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.15s; }
.stat-card:nth-child(3) { animation-delay: 0.2s; }
.stat-card:nth-child(4) { animation-delay: 0.25s; }

.result-item {
    animation: elegantSlideUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

.search-container {
    animation: elegantSlideUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

/* Focus States with Elegant Glow */
.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    animation: elegantGlow 3s ease-in-out infinite;
}

/* Improved Loading States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Parallax Effect for Hero */
.hero {
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
}

/* Smooth Scrolling Enhancements */
@media (prefers-reduced-motion: no-preference) {
    html {
        scroll-behavior: smooth;
    }

    * {
        transition: var(--transition);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1f2937;
        --gray-100: #374151;
        --gray-200: #4b5563;
        --gray-300: #6b7280;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
        --light-color: #111827;
    }

    .navbar {
        background: rgba(31, 41, 55, 0.95);
        border-bottom-color: var(--gray-700);
    }

    .search-container,
    .stat-card,
    .stats-section,
    .scan-result {
        background: var(--gray-800);
        color: var(--gray-100);
    }

    .stat-card {
        background: #374151;
        border-color: rgba(75, 85, 99, 0.5);
        color: var(--gray-100);
    }

    .stat-card:hover {
        border-color: rgba(59, 130, 246, 0.5);
    }

    .stat-number {
        color: var(--gray-100);
    }

    .stat-card:hover .stat-number {
        color: #60a5fa;
    }

    .stats-section {
        background: #374151;
        border-color: rgba(75, 85, 99, 0.5);
        color: var(--gray-100);
    }

    .stats-section:hover {
        border-color: rgba(59, 130, 246, 0.5);
    }

    .stats-result {
        background: #1f2937;
        border-color: rgba(75, 85, 99, 0.5);
    }

    .stats-result:hover {
        background: #374151;
        border-color: rgba(59, 130, 246, 0.3);
    }

    .stats-result-item {
        background: rgba(31, 41, 55, 0.8);
        border-color: rgba(75, 85, 99, 0.5);
    }

    .stats-result-list-item {
        background: rgba(31, 41, 55, 0.6);
        border-color: rgba(75, 85, 99, 0.3);
    }
}

/* Additional Elegant Effects */
.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 1px;
    background: var(--gray-200);
    opacity: 0.5;
}

/* Elegant Card Styles */
.search-container,
.stat-card,
.stats-section,
.scan-result {
    border: 1px solid var(--gray-100);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.search-container::before,
.stat-card::before,
.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent);
}

/* Smooth Scroll Enhancement */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 5rem;
}

/* Enhanced Typography */
.hero-title {
    letter-spacing: -0.02em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
    letter-spacing: -0.01em;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 3rem;
    height: 2px;
    background: var(--accent-color);
    border-radius: 1px;
}

/* Scroll animations */
.section-header {
    animation: elegantSlideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

.section-header .section-title {
    animation-delay: 0.1s;
}

.section-header .section-description {
    animation-delay: 0.2s;
}

/* Improved focus states */
.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
    outline: none;
}

/* Enhanced card styles */
.stat-card,
.search-container,
.stats-section {
    border: 1px solid var(--gray-200);
    transition: var(--transition-smooth);
}

.stat-card:hover,
.search-container:hover,
.stats-section:hover {
    border-color: var(--gray-300);
}

/* Mobile cursor handling */
@media (hover: none) and (pointer: coarse) {
    .custom-cursor {
        display: none;
    }

    body, *, *::before, *::after {
        cursor: auto !important;
    }
}

/* Mobile Responsive Styles for Statistics */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        padding: 2rem 1.5rem;
        flex-direction: row;
        text-align: left;
        gap: 1.5rem;
    }

    .stat-icon {
        width: 4rem;
        height: 4rem;
        margin-bottom: 0;
        flex-shrink: 0;
    }

    .stat-content {
        align-items: flex-start;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stats-details {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stats-section {
        padding: 2rem 1.5rem;
    }

    .stats-result-grid {
        grid-template-columns: 1fr;
    }

    .stats-result-item {
        padding: 0.75rem;
    }

    .stats-result-icon {
        width: 2rem;
        height: 2rem;
        font-size: 0.875rem;
    }

    .stats-result-number {
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .stat-card {
        padding: 1.5rem 1rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .stat-icon {
        width: 3.5rem;
        height: 3.5rem;
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .stat-content {
        align-items: center;
    }

    .stat-number {
        font-size: 1.75rem;
    }

    .stats-section {
        padding: 1.5rem 1rem;
    }

    .stats-result-list-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }
    
    .nav-menu {
        display: none;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-actions {
        justify-content: center;
    }
    
    .container {
        padding: 0 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .stats-details {
        grid-template-columns: 1fr;
    }
    
    .input-group {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .search-tabs {
        flex-direction: column;
    }
    
    .tab-content {
        padding: 1rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-large {
        width: 100%;
        justify-content: center;
    }
}
