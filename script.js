// Custom Cursor Implementation
class CustomCursor {
    constructor() {
        this.cursor = document.getElementById('customCursor');
        this.isVisible = false;
        this.isHovering = false;
        this.isClicking = false;
        this.isTextMode = false;

        this.init();
    }

    init() {
        // Mouse move handler
        document.addEventListener('mousemove', (e) => {
            const magneticElement = this.findMagneticElement(e.target);
            if (magneticElement) {
                const rect = magneticElement.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                const distance = Math.sqrt(Math.pow(e.clientX - centerX, 2) + Math.pow(e.clientY - centerY, 2));

                if (distance < 50) {
                    // Magnetic effect - pull cursor towards center
                    const pullStrength = 0.3;
                    const pullX = centerX + (e.clientX - centerX) * (1 - pullStrength);
                    const pullY = centerY + (e.clientY - centerY) * (1 - pullStrength);
                    this.updatePosition(pullX, pullY);
                } else {
                    this.updatePosition(e.clientX, e.clientY);
                }
            } else {
                this.updatePosition(e.clientX, e.clientY);
            }
            this.show();
        });

        // Mouse leave handler
        document.addEventListener('mouseleave', () => {
            this.hide();
        });

        // Mouse enter handler
        document.addEventListener('mouseenter', () => {
            this.show();
        });

        // Click handlers
        document.addEventListener('mousedown', (e) => {
            this.setClickState(true);
            this.createRipple(e.clientX, e.clientY);
        });

        document.addEventListener('mouseup', () => {
            this.setClickState(false);
        });

        // Hover handlers for interactive elements
        this.addHoverListeners();

        // Text input handlers
        this.addTextInputListeners();
    }

    updatePosition(x, y) {
        if (this.cursor) {
            this.cursor.style.left = x + 'px';
            this.cursor.style.top = y + 'px';
        }
    }

    show() {
        if (!this.isVisible && this.cursor) {
            this.cursor.style.opacity = '1';
            this.isVisible = true;
        }
    }

    hide() {
        if (this.isVisible && this.cursor) {
            this.cursor.style.opacity = '0';
            this.isVisible = false;
        }
    }

    setHoverState(isHovering) {
        if (this.isHovering !== isHovering && this.cursor) {
            this.isHovering = isHovering;
            this.cursor.classList.toggle('hover', isHovering);
        }
    }

    setClickState(isClicking) {
        if (this.isClicking !== isClicking && this.cursor) {
            this.isClicking = isClicking;
            this.cursor.classList.toggle('click', isClicking);
        }
    }

    setTextMode(isTextMode) {
        if (this.isTextMode !== isTextMode && this.cursor) {
            this.isTextMode = isTextMode;
            this.cursor.classList.toggle('text', isTextMode);
        }
    }

    addHoverListeners() {
        const interactiveElements = 'a, button, .btn, [role="button"], .nav-link, .tab-btn, .result-item, .stat-card, .upload-area';

        document.addEventListener('mouseover', (e) => {
            if (e.target.matches(interactiveElements) || e.target.closest(interactiveElements)) {
                this.setHoverState(true);

                // Add special effects for specific elements
                if (e.target.closest('.btn-primary')) {
                    this.cursor.style.background = 'var(--accent-light)';
                } else if (e.target.closest('.btn-outline')) {
                    this.cursor.style.background = 'transparent';
                    this.cursor.style.border = '2px solid var(--accent-color)';
                } else if (e.target.closest('.stat-card')) {
                    this.cursor.style.background = 'var(--accent-color)';
                } else if (e.target.closest('.upload-area')) {
                    this.cursor.style.background = 'var(--accent-dark)';
                }
            }
        });

        document.addEventListener('mouseout', (e) => {
            if (e.target.matches(interactiveElements) || e.target.closest(interactiveElements)) {
                this.setHoverState(false);
                // Reset cursor style
                this.cursor.style.background = 'var(--accent-color)';
                this.cursor.style.border = 'none';
            }
        });

        // Add loading state for buttons during API calls
        document.addEventListener('click', (e) => {
            if (e.target.matches('button, .btn') || e.target.closest('button, .btn')) {
                this.setLoadingState(true);
                setTimeout(() => this.setLoadingState(false), 1000);
            }
        });
    }

    setLoadingState(isLoading) {
        if (this.cursor) {
            this.cursor.classList.toggle('loading', isLoading);
        }
    }

    createRipple(x, y) {
        // Create ripple effect
        this.cursor.classList.add('ripple');
        setTimeout(() => {
            this.cursor.classList.remove('ripple');
        }, 600);

        // Create additional ripple element for better effect
        const ripple = document.createElement('div');
        ripple.style.position = 'fixed';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.style.width = '4px';
        ripple.style.height = '4px';
        ripple.style.background = 'rgba(220, 38, 38, 0.6)';
        ripple.style.borderRadius = '50%';
        ripple.style.transform = 'translate(-50%, -50%)';
        ripple.style.pointerEvents = 'none';
        ripple.style.zIndex = '9998';
        ripple.style.animation = 'ripple 0.6s ease-out forwards';

        document.body.appendChild(ripple);

        setTimeout(() => {
            document.body.removeChild(ripple);
        }, 600);
    }

    findMagneticElement(element) {
        const magneticSelectors = '.btn, button, .nav-link';
        return element.closest(magneticSelectors);
    }

    addTextInputListeners() {
        const textElements = 'input[type="text"], input[type="number"], textarea, input[type="search"]';

        document.addEventListener('mouseover', (e) => {
            if (e.target.matches(textElements)) {
                this.setTextMode(true);
            }
        });

        document.addEventListener('mouseout', (e) => {
            if (e.target.matches(textElements)) {
                this.setTextMode(false);
            }
        });
    }
}

// Initialize custom cursor when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CustomCursor();
});

// API Configuration (using config.js)
const API_BASE_URL = CONFIG?.API_BASE_URL || '/plugsec/api/v1';
let authToken = localStorage.getItem(CONFIG?.STORAGE_KEYS?.AUTH_TOKEN || 'authToken');
let apiKey = localStorage.getItem(CONFIG?.STORAGE_KEYS?.API_KEY || 'apiKey');

// DOM Elements
const elements = {
    loginBtn: document.getElementById('loginBtn'),
    logoutBtn: document.getElementById('logoutBtn'),
    userProfile: document.getElementById('userProfile'),
    userAvatar: document.getElementById('userAvatar'),
    userName: document.getElementById('userName'),
    loadingOverlay: document.getElementById('loadingOverlay'),
    toastContainer: document.getElementById('toastContainer'),
    
    // Search elements
    hashInput: document.getElementById('hashInput'),
    searchHashBtn: document.getElementById('searchHashBtn'),
    batchHashes: document.getElementById('batchHashes'),
    searchBatchBtn: document.getElementById('searchBatchBtn'),
    queryInput: document.getElementById('queryInput'),
    verdictSelect: document.getElementById('verdictSelect'),
    approvedSelect: document.getElementById('approvedSelect'),
    limitInput: document.getElementById('limitInput'),
    searchAdvancedBtn: document.getElementById('searchAdvancedBtn'),
    searchResults: document.getElementById('searchResults'),
    resultsList: document.getElementById('resultsList'),
    resultsCount: document.getElementById('resultsCount'),
    
    // Stats elements
    totalScanned: document.getElementById('totalScanned'),
    safePlugins: document.getElementById('safePlugins'),
    dangerousPlugins: document.getElementById('dangerousPlugins'),
    totalDevelopers: document.getElementById('totalDevelopers'),
    developerIdInput: document.getElementById('developerIdInput'),
    getDeveloperStatsBtn: document.getElementById('getDeveloperStatsBtn'),
    developerStats: document.getElementById('developerStats'),
    channelIdInput: document.getElementById('channelIdInput'),
    getChannelStatsBtn: document.getElementById('getChannelStatsBtn'),
    channelStats: document.getElementById('channelStats'),
    
    // Scan elements
    uploadArea: document.getElementById('uploadArea'),
    fileInput: document.getElementById('fileInput'),
    scanProgress: document.getElementById('scanProgress'),
    scanResult: document.getElementById('scanResult')
};

// Utility Functions
function showLoading() {
    elements.loadingOverlay.classList.remove('hidden');
}

function hideLoading() {
    elements.loadingOverlay.classList.add('hidden');
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    
    elements.toastContainer.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'slideOut 0.3s ease-in forwards';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 5000);
}

function scrollToSection(sectionId) {
    document.getElementById(sectionId).scrollIntoView({ behavior: 'smooth' });
}

// API Functions
async function makeRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };
    
    if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
    }
    
    if (apiKey) {
        headers['X-API-Key'] = apiKey;
    }
    
    try {
        const response = await fetch(url, {
            ...options,
            headers
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
}

// Authentication
function updateAuthUI() {
    if (authToken) {
        elements.loginBtn.classList.add('hidden');
        elements.userProfile.classList.remove('hidden');
        loadUserProfile();
    } else {
        elements.loginBtn.classList.remove('hidden');
        elements.userProfile.classList.add('hidden');
    }
}

async function loadUserProfile() {
    try {
        const response = await makeRequest('/users/me');
        if (response.status === 'success') {
            const userData = response.data;
            elements.userName.textContent = userData.first_name || 'Пользователь';
            if (userData.photo_url) {
                elements.userAvatar.src = userData.photo_url;
            }

            // Сохраняем профиль пользователя в localStorage
            saveToLocalStorage(CONFIG.STORAGE_KEYS.USER_PROFILE, userData);
        }
    } catch (error) {
        console.error('Failed to load user profile:', error);
        handleApiError(error, 'при загрузке профиля');

        // Если токен недействителен, выходим из системы
        if (error.message.includes('401')) {
            logout();
        }
    }
}

function logout() {
    authToken = null;
    localStorage.removeItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
    localStorage.removeItem(CONFIG.STORAGE_KEYS.USER_PROFILE);
    updateAuthUI();
    showToast(CONFIG.getSuccessMessage('LOGOUT_SUCCESS'), 'info');
}

// Telegram Login Widget Integration
function initTelegramLogin() {
    // Создаем контейнер для Telegram виджета
    const telegramContainer = document.createElement('div');
    telegramContainer.id = 'telegram-login-container';
    telegramContainer.className = 'telegram-login-container';

    // Добавляем виджет Telegram
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://telegram.org/js/telegram-widget.js?22';
    script.setAttribute('data-telegram-login', 'YOUR_BOT_USERNAME'); // Замените на имя вашего бота
    script.setAttribute('data-size', 'large');
    script.setAttribute('data-radius', '10');
    script.setAttribute('data-onauth', 'onTelegramAuth(user)');
    script.setAttribute('data-request-access', 'write');

    telegramContainer.appendChild(script);

    // Заменяем кнопку входа на виджет Telegram
    if (elements.loginBtn) {
        elements.loginBtn.style.display = 'none';
        elements.loginBtn.parentNode.insertBefore(telegramContainer, elements.loginBtn.nextSibling);
    }
}

// Обработчик авторизации через Telegram
window.onTelegramAuth = async function(user) {
    console.log('Telegram auth data:', user);

    try {
        showLoading();

        // Отправляем данные авторизации на сервер
        const response = await fetch(`${API_BASE_URL}/auth/telegram`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(user)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.access_token) {
            // Сохраняем токен
            authToken = result.access_token;
            localStorage.setItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN, authToken);

            // Обновляем UI
            updateAuthUI();

            showToast('Успешная авторизация через Telegram!', 'success');
        } else {
            throw new Error('Не получен токен авторизации');
        }

    } catch (error) {
        console.error('Telegram auth error:', error);
        showToast('Ошибка авторизации через Telegram: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
};

// Альтернативный метод авторизации через модальное окно
async function showTelegramLoginModal() {
    const modal = createModal('telegram-login-modal', 'Авторизация через Telegram');

    const modalContent = `
        <div class="telegram-auth-content">
            <div class="auth-header">
                <i class="fab fa-telegram telegram-icon"></i>
                <h3>Войти через Telegram</h3>
                <p>Для доступа к полному функционалу необходима авторизация</p>
            </div>

            <div class="auth-methods">
                <div class="auth-method">
                    <h4>Метод 1: Telegram Widget</h4>
                    <div id="telegram-widget-container"></div>
                </div>

                <div class="auth-method">
                    <h4>Метод 2: Ручной ввод данных</h4>
                    <form id="manual-auth-form" class="manual-auth-form">
                        <div class="form-group">
                            <label for="telegram-id">Telegram ID:</label>
                            <input type="number" id="telegram-id" name="id" required>
                        </div>
                        <div class="form-group">
                            <label for="telegram-first-name">Имя:</label>
                            <input type="text" id="telegram-first-name" name="first_name" required>
                        </div>
                        <div class="form-group">
                            <label for="telegram-username">Username (опционально):</label>
                            <input type="text" id="telegram-username" name="username">
                        </div>
                        <div class="form-group">
                            <label for="telegram-photo-url">URL фото (опционально):</label>
                            <input type="url" id="telegram-photo-url" name="photo_url">
                        </div>
                        <div class="form-group">
                            <label for="telegram-auth-date">Дата авторизации:</label>
                            <input type="number" id="telegram-auth-date" name="auth_date" readonly>
                        </div>
                        <div class="form-group">
                            <label for="telegram-hash">Hash:</label>
                            <input type="text" id="telegram-hash" name="hash" required>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i>
                            Войти
                        </button>
                    </form>
                </div>
            </div>

            <div class="auth-help">
                <h4>Как получить данные для авторизации?</h4>
                <ol>
                    <li>Откройте бота @YOUR_BOT_USERNAME в Telegram</li>
                    <li>Отправьте команду /start</li>
                    <li>Скопируйте предоставленные данные</li>
                    <li>Вставьте их в форму выше</li>
                </ol>
            </div>
        </div>
    `;

    modal.querySelector('.modal-body').innerHTML = modalContent;

    // Устанавливаем текущую дату авторизации
    const authDateInput = modal.querySelector('#telegram-auth-date');
    authDateInput.value = Math.floor(Date.now() / 1000);

    // Обработчик формы ручного ввода
    const manualForm = modal.querySelector('#manual-auth-form');
    manualForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = new FormData(manualForm);
        const authData = Object.fromEntries(formData.entries());

        // Преобразуем числовые поля
        authData.id = parseInt(authData.id);
        authData.auth_date = parseInt(authData.auth_date);

        try {
            await authenticateWithTelegram(authData);
            closeModal(modal);
        } catch (error) {
            showToast('Ошибка авторизации: ' + error.message, 'error');
        }
    });

    showModal(modal);
}

// Search Functions
async function searchByHash() {
    const hash = elements.hashInput.value.trim();
    if (!hash) {
        showToast('Введите хэш файла', 'warning');
        return;
    }
    
    showLoading();
    try {
        const response = await makeRequest(`/report/${hash}`);
        displaySearchResults([response.data]);
    } catch (error) {
        showToast('Ошибка поиска по хэшу', 'error');
    } finally {
        hideLoading();
    }
}

async function searchBatch() {
    const hashesText = elements.batchHashes.value.trim();
    if (!hashesText) {
        showToast('Введите хэши файлов', 'warning');
        return;
    }
    
    const hashes = hashesText.split('\n').map(h => h.trim()).filter(h => h);
    if (hashes.length === 0) {
        showToast('Введите корректные хэши', 'warning');
        return;
    }
    
    showLoading();
    try {
        const response = await makeRequest('/reports/batch', {
            method: 'POST',
            body: JSON.stringify({ hashes })
        });
        displaySearchResults(response.data);
    } catch (error) {
        showToast('Ошибка пакетного поиска', 'error');
    } finally {
        hideLoading();
    }
}

async function searchAdvanced() {
    if (!authToken) {
        showToast('Для расширенного поиска необходима авторизация', 'warning');
        return;
    }
    
    const params = new URLSearchParams();
    
    const query = elements.queryInput.value.trim();
    if (query) params.append('query', query);
    
    const verdict = elements.verdictSelect.value;
    if (verdict) params.append('verdict', verdict);
    
    const approved = elements.approvedSelect.value;
    if (approved) params.append('approved', approved);
    
    const limit = elements.limitInput.value;
    if (limit) params.append('limit', limit);
    
    showLoading();
    try {
        const response = await makeRequest(`/reports/search?${params.toString()}`);
        displaySearchResults(response.data.results || response.data);
        elements.resultsCount.textContent = response.data.total || response.data.length;
    } catch (error) {
        showToast('Ошибка расширенного поиска', 'error');
    } finally {
        hideLoading();
    }
}

function displaySearchResults(results) {
    if (!Array.isArray(results)) {
        results = [results];
    }
    
    elements.searchResults.classList.remove('hidden');
    elements.resultsCount.textContent = results.length;
    
    elements.resultsList.innerHTML = results.map(result => `
        <div class="result-item">
            <div class="result-header">
                <div class="result-hash">${result.file_hash || 'N/A'}</div>
                <div class="verdict-badge verdict-${getVerdictClass(result.verdict)}">
                    ${result.verdict || 'Неизвестно'}
                </div>
            </div>
            <div class="result-content">
                <h4>${result.plugin_name || 'Неизвестный плагин'}</h4>
                <p><strong>Размер:</strong> ${result.file_size || 'N/A'} байт</p>
                <p><strong>Дата сканирования:</strong> ${formatDate(result.scan_date)}</p>
                ${result.description ? `<p><strong>Описание:</strong> ${result.description}</p>` : ''}
                ${result.approved !== undefined ? `<p><strong>Одобрено:</strong> ${result.approved ? 'Да' : 'Нет'}</p>` : ''}
            </div>
        </div>
    `).join('');
}

function getVerdictClass(verdict) {
    if (!verdict) return 'warning';
    const lower = verdict.toLowerCase();
    if (lower.includes('безопасно') || lower.includes('safe')) return 'safe';
    if (lower.includes('опасно') || lower.includes('danger')) return 'danger';
    return 'warning';
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('ru-RU');
}

// Statistics Functions
async function loadGlobalStats() {
    try {
        // Добавляем состояние загрузки
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach(card => card.classList.add('loading'));

        const response = await makeRequest('/stats');
        if (response.status === 'success') {
            const stats = response.data;

            // Убираем состояние загрузки и добавляем состояние обновления
            statCards.forEach(card => {
                card.classList.remove('loading');
                card.classList.add('updating');
            });

            // Анимированное обновление счетчиков
            animateCounter(elements.totalScanned, stats.total_scanned || 0);
            animateCounter(elements.safePlugins, stats.safe_plugins || 0);
            animateCounter(elements.dangerousPlugins, stats.dangerous_plugins || 0);
            animateCounter(elements.totalDevelopers, stats.total_developers || 0);

            // Убираем состояние обновления через некоторое время
            setTimeout(() => {
                statCards.forEach(card => card.classList.remove('updating'));
            }, 1500);
        }
    } catch (error) {
        console.error('Failed to load global stats:', error);
        // Убираем состояние загрузки при ошибке
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach(card => card.classList.remove('loading'));
    }
}

// Функция анимации счетчика
function animateCounter(element, targetValue, duration = 1500) {
    const startValue = parseInt(element.textContent.replace(/,/g, '')) || 0;
    const increment = (targetValue - startValue) / (duration / 16);
    let currentValue = startValue;

    const timer = setInterval(() => {
        currentValue += increment;

        if ((increment > 0 && currentValue >= targetValue) ||
            (increment < 0 && currentValue <= targetValue)) {
            currentValue = targetValue;
            clearInterval(timer);
        }

        element.textContent = Math.floor(currentValue).toLocaleString();
    }, 16);
}

// Автоматическое обновление статистики
let statsUpdateInterval;

function startStatsAutoUpdate() {
    // Обновляем статистику каждые 30 секунд
    statsUpdateInterval = setInterval(() => {
        if (document.getElementById('stats').offsetParent !== null) {
            loadGlobalStats();
        }
    }, 30000);
}

function stopStatsAutoUpdate() {
    if (statsUpdateInterval) {
        clearInterval(statsUpdateInterval);
        statsUpdateInterval = null;
    }
}

async function getDeveloperStats() {
    const developerId = elements.developerIdInput.value.trim();
    if (!developerId) {
        showToast('Введите ID разработчика', 'warning');
        return;
    }
    
    showLoading();
    try {
        const response = await makeRequest(`/developer/${developerId}/stats`);
        displayStatsResult(response.data, elements.developerStats);
    } catch (error) {
        showToast('Ошибка получения статистики разработчика', 'error');
    } finally {
        hideLoading();
    }
}

async function getChannelStats() {
    const channelId = elements.channelIdInput.value.trim();
    if (!channelId) {
        showToast('Введите ID канала', 'warning');
        return;
    }
    
    showLoading();
    try {
        const response = await makeRequest(`/channel/${channelId}/stats`);
        displayStatsResult(response.data, elements.channelStats);
    } catch (error) {
        showToast('Ошибка получения статистики канала', 'error');
    } finally {
        hideLoading();
    }
}

function displayStatsResult(data, container) {
    container.classList.remove('hidden');

    // Создаем красивое отображение статистики
    let statsHtml = '<div class="stats-result-content">';

    if (data.total_plugins !== undefined) {
        statsHtml += `
            <div class="stats-result-grid">
                <div class="stats-result-item">
                    <div class="stats-result-icon">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <div class="stats-result-info">
                        <div class="stats-result-number">${data.total_plugins || 0}</div>
                        <div class="stats-result-label">Всего плагинов</div>
                    </div>
                </div>

                <div class="stats-result-item">
                    <div class="stats-result-icon safe">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-result-info">
                        <div class="stats-result-number">${data.safe_plugins || 0}</div>
                        <div class="stats-result-label">Безопасных</div>
                    </div>
                </div>

                <div class="stats-result-item">
                    <div class="stats-result-icon danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stats-result-info">
                        <div class="stats-result-number">${data.dangerous_plugins || 0}</div>
                        <div class="stats-result-label">Опасных</div>
                    </div>
                </div>
            </div>
        `;
    }

    if (data.channels && data.channels.length > 0) {
        statsHtml += `
            <div class="stats-result-section">
                <h5><i class="fas fa-hashtag"></i> Каналы</h5>
                <div class="stats-result-list">
                    ${data.channels.map(channel => `
                        <div class="stats-result-list-item">
                            <span class="channel-name">${channel.name || 'Неизвестный канал'}</span>
                            <span class="channel-count">${channel.plugin_count || 0} плагинов</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    if (data.recent_plugins && data.recent_plugins.length > 0) {
        statsHtml += `
            <div class="stats-result-section">
                <h5><i class="fas fa-clock"></i> Последние плагины</h5>
                <div class="stats-result-list">
                    ${data.recent_plugins.slice(0, 5).map(plugin => `
                        <div class="stats-result-list-item">
                            <span class="plugin-name">${plugin.name || 'Неизвестный плагин'}</span>
                            <span class="plugin-status ${plugin.is_safe ? 'safe' : 'danger'}">
                                <i class="fas fa-${plugin.is_safe ? 'check' : 'times'}"></i>
                                ${plugin.is_safe ? 'Безопасен' : 'Опасен'}
                            </span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // Если нет специальных данных, показываем JSON
    if (!data.total_plugins && !data.channels && !data.recent_plugins) {
        statsHtml += `
            <div class="stats-result-raw">
                <h5><i class="fas fa-code"></i> Данные</h5>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            </div>
        `;
    }

    statsHtml += '</div>';
    container.innerHTML = statsHtml;
}

// File Upload Functions
async function uploadFile(file) {
    if (!authToken) {
        showToast('Для загрузки файлов необходима авторизация', 'warning');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    elements.scanProgress.classList.remove('hidden');
    elements.scanResult.classList.add('hidden');
    
    try {
        const response = await fetch(`${API_BASE_URL}/scan/upload`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`
            },
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        displayScanResult(result.data);
    } catch (error) {
        showToast('Ошибка загрузки файла', 'error');
    } finally {
        elements.scanProgress.classList.add('hidden');
    }
}

function displayScanResult(result) {
    elements.scanResult.classList.remove('hidden');

    const fileExtension = result.filename ? result.filename.split('.').pop().toLowerCase() : '';
    const fileIcon = getFileIcon(fileExtension);
    const fileTypeLabel = getFileTypeLabel(fileExtension);

    elements.scanResult.innerHTML = `
        <div class="scan-result-header">
            <h3><i class="fas fa-shield-check"></i> Результат сканирования</h3>
        </div>
        <div class="result-item">
            <div class="result-header">
                <div class="file-info">
                    <div class="file-icon">
                        <i class="${fileIcon}"></i>
                    </div>
                    <div class="file-details">
                        <div class="file-name">${result.filename || 'N/A'}</div>
                        <div class="file-type">${fileTypeLabel}</div>
                    </div>
                </div>
                <div class="verdict-badge verdict-${getVerdictClass(result.verdict)}">
                    <i class="fas fa-${getVerdictIcon(result.verdict)}"></i>
                    ${result.verdict || 'Обрабатывается...'}
                </div>
            </div>
            <div class="result-content">
                <div class="result-grid">
                    <div class="result-field">
                        <label>Хэш файла:</label>
                        <span class="hash-value">${result.file_hash || 'N/A'}</span>
                    </div>
                    <div class="result-field">
                        <label>Размер файла:</label>
                        <span>${formatFileSize(result.file_size)}</span>
                    </div>
                    <div class="result-field">
                        <label>Статус обработки:</label>
                        <span class="status-${result.status?.toLowerCase()}">${result.status || 'В обработке'}</span>
                    </div>
                    ${result.scan_time ? `
                    <div class="result-field">
                        <label>Время сканирования:</label>
                        <span>${new Date(result.scan_time).toLocaleString()}</span>
                    </div>
                    ` : ''}
                </div>
                ${result.description ? `
                <div class="result-description">
                    <label>Описание:</label>
                    <p>${result.description}</p>
                </div>
                ` : ''}
                ${result.threats && result.threats.length > 0 ? `
                <div class="threats-section">
                    <label>Обнаруженные угрозы:</label>
                    <ul class="threats-list">
                        ${result.threats.map(threat => `
                            <li class="threat-item">
                                <i class="fas fa-exclamation-triangle"></i>
                                ${threat}
                            </li>
                        `).join('')}
                    </ul>
                </div>
                ` : ''}
            </div>
        </div>
    `;
}

// Get file icon based on extension
function getFileIcon(extension) {
    const icons = {
        'plugin': 'fas fa-puzzle-piece',
        'py': 'fab fa-python'
    };
    return icons[extension] || 'fas fa-file-question';
}

// Get file type label
function getFileTypeLabel(extension) {
    const labels = {
        'plugin': 'Файл плагина',
        'py': 'Python плагин'
    };
    return labels[extension] || 'Неподдерживаемый тип';
}

// Get verdict icon
function getVerdictIcon(verdict) {
    if (!verdict) return 'clock';
    const v = verdict.toLowerCase();
    if (v.includes('safe') || v.includes('безопасн')) return 'check';
    if (v.includes('danger') || v.includes('опасн') || v.includes('malware')) return 'times';
    if (v.includes('warning') || v.includes('подозр')) return 'exclamation-triangle';
    return 'question';
}

// Format file size
function formatFileSize(bytes) {
    if (!bytes || bytes === 'N/A') return 'N/A';
    const sizes = ['байт', 'КБ', 'МБ', 'ГБ'];
    if (bytes === 0) return '0 байт';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

// Tab Management
function initTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const tabId = btn.dataset.tab;
            
            // Update active tab button
            tabBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            // Update active tab pane
            tabPanes.forEach(pane => pane.classList.remove('active'));
            document.getElementById(`${tabId}-tab`).classList.add('active');
        });
    });
}

// Navigation
function initNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            scrollToSection(targetId);
            
            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            link.classList.add('active');
        });
    });
    
    // Update active nav link on scroll
    window.addEventListener('scroll', () => {
        const sections = document.querySelectorAll('section[id]');
        const scrollPos = window.scrollY + 100;
        
        sections.forEach(section => {
            const top = section.offsetTop;
            const bottom = top + section.offsetHeight;
            const id = section.getAttribute('id');
            
            if (scrollPos >= top && scrollPos <= bottom) {
                navLinks.forEach(l => l.classList.remove('active'));
                const activeLink = document.querySelector(`.nav-link[href="#${id}"]`);
                if (activeLink) activeLink.classList.add('active');
            }
        });
    });
}

// File Upload Drag & Drop
function initFileUpload() {
    const uploadArea = elements.uploadArea;
    const fileInput = elements.fileInput;

    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');

        // Show file type hint
        const items = e.dataTransfer.items;
        if (items && items.length > 0) {
            const file = items[0];
            if (file.kind === 'file') {
                const fileName = file.getAsFile()?.name || '';
                const extension = fileName.split('.').pop()?.toLowerCase();
                updateDragHint(extension);
            }
        }
    });

    uploadArea.addEventListener('dragleave', (e) => {
        // Only remove dragover if we're leaving the upload area completely
        if (!uploadArea.contains(e.relatedTarget)) {
            uploadArea.classList.remove('dragover');
            clearDragHint();
        }
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        clearDragHint();

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            const extension = file.name.split('.').pop()?.toLowerCase();

            // Show file type before upload
            showFilePreview(file, extension);

            // Use enhanced upload function
            uploadFileEnhanced(file);
        }
    });

    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            const file = e.target.files[0];
            const extension = file.name.split('.').pop()?.toLowerCase();

            // Show file type before upload
            showFilePreview(file, extension);

            // Use enhanced upload function
            uploadFileEnhanced(file);
        }
    });
}

// Update drag hint based on file type
function updateDragHint(extension) {
    const uploadText = document.querySelector('.upload-text p');
    if (!uploadText) return;

    const fileTypeLabel = getFileTypeLabel(extension);
    const isSupported = (CONFIG?.ALLOWED_EXTENSIONS || ['plugin', 'py']).includes(extension);

    if (isSupported) {
        uploadText.innerHTML = `<span style="color: var(--success-color);"><i class="fas fa-check"></i> ${fileTypeLabel} - Поддерживается</span>`;
    } else {
        uploadText.innerHTML = `<span style="color: var(--danger-color);"><i class="fas fa-times"></i> ${fileTypeLabel} - Не поддерживается</span>`;
    }
}

// Clear drag hint
function clearDragHint() {
    const uploadText = document.querySelector('.upload-text p');
    if (!uploadText) return;

    uploadText.innerHTML = 'Поддерживаются только файлы: .plugin и .py';
}

// Show file preview before upload
function showFilePreview(file, extension) {
    const uploadText = document.querySelector('.upload-text');
    if (!uploadText) return;

    const fileIcon = getFileIcon(extension);
    const fileTypeLabel = getFileTypeLabel(extension);
    const fileSize = formatFileSize(file.size);

    uploadText.innerHTML = `
        <div class="file-preview">
            <div class="file-preview-icon">
                <i class="${fileIcon}"></i>
            </div>
            <div class="file-preview-info">
                <div class="file-preview-name">${file.name}</div>
                <div class="file-preview-details">${fileTypeLabel} • ${fileSize}</div>
            </div>
        </div>
        <p style="margin-top: 1rem; color: var(--success-color);">
            <i class="fas fa-upload"></i> Загружаем файл...
        </p>
    `;
}

// Modal Management
function createModal(id, title) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.id = id;

    modal.innerHTML = `
        <div class="modal">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="modal-close" onclick="closeModal(this.closest('.modal-overlay'))">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body"></div>
        </div>
    `;

    // Закрытие по клику на overlay
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal(modal);
        }
    });

    return modal;
}

function showModal(modal) {
    document.body.appendChild(modal);
    setTimeout(() => modal.classList.add('show'), 10);
}

function closeModal(modal) {
    modal.classList.remove('show');
    setTimeout(() => {
        if (modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }
    }, 300);
}

// Улучшенная функция авторизации через Telegram
async function authenticateWithTelegram(authData) {
    try {
        showLoading();

        console.log('Authenticating with Telegram data:', authData);

        const response = await fetch(`${API_BASE_URL}/auth/telegram`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(authData)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const result = await response.json();

        if (result.access_token) {
            // Сохраняем токен
            authToken = result.access_token;
            localStorage.setItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN, authToken);

            // Обновляем UI
            updateAuthUI();

            showToast('Успешная авторизация через Telegram!', 'success');

            // Загружаем профиль пользователя
            await loadUserProfile();

        } else {
            throw new Error('Не получен токен авторизации');
        }

    } catch (error) {
        console.error('Telegram auth error:', error);
        throw error;
    } finally {
        hideLoading();
    }
}

// Проверка валидности токена
async function validateToken() {
    if (!authToken) return false;

    try {
        const response = await makeRequest('/users/me');
        return response.status === 'success';
    } catch (error) {
        console.error('Token validation failed:', error);
        return false;
    }
}

// Автоматическая проверка токена при загрузке
async function checkAuthStatus() {
    if (authToken) {
        const isValid = await validateToken();
        if (!isValid) {
            logout();
            showToast('Сессия истекла. Пожалуйста, войдите снова', 'warning');
        }
    }
}

// Event Listeners
function initEventListeners() {
    // Auth events
    elements.logoutBtn?.addEventListener('click', logout);
    elements.loginBtn?.addEventListener('click', showTelegramLoginModal);

    // Search events
    elements.searchHashBtn?.addEventListener('click', searchByHash);
    elements.searchBatchBtn?.addEventListener('click', searchBatch);
    elements.searchAdvancedBtn?.addEventListener('click', searchAdvanced);

    // Stats events
    elements.getDeveloperStatsBtn?.addEventListener('click', getDeveloperStats);
    elements.getChannelStatsBtn?.addEventListener('click', getChannelStats);

    // Enter key support for inputs
    elements.hashInput?.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') searchByHash();
    });

    elements.developerIdInput?.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') getDeveloperStats();
    });

    elements.channelIdInput?.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') getChannelStats();
    });
}

// Initialize Application
async function init() {
    updateAuthUI();
    initTabs();
    initNavigation();
    initFileUpload();
    initEventListeners();
    loadGlobalStats();
    startStatsAutoUpdate();

    // Проверяем статус авторизации
    await checkAuthStatus();

    // Инициализируем Telegram Login если нужно
    if (!authToken) {
        // Можно раскомментировать для автоматической инициализации виджета
        // initTelegramLogin();
    }
}

// Start the application when DOM is loaded
document.addEventListener('DOMContentLoaded', init);

// Additional Features
function setButtonLoading(button, loading = true) {
    if (loading) {
        button.disabled = true;
        button.classList.add('loading');
    } else {
        button.disabled = false;
        button.classList.remove('loading');
    }
}

// Enhanced Search with Debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Auto-complete for search
const debouncedSearch = debounce(async (query) => {
    if (query.length > 2 && authToken) {
        try {
            const response = await makeRequest(`/reports/search?query=${encodeURIComponent(query)}&limit=5`);
            // Could implement dropdown suggestions here
        } catch (error) {
            console.error('Auto-complete search failed:', error);
        }
    }
}, 300);

// Enhanced File Validation
function validateFile(file) {
    const maxSize = CONFIG?.MAX_FILE_SIZE || 50 * 1024 * 1024; // 50MB
    const allowedTypes = CONFIG?.ALLOWED_MIME_TYPES || [
        'application/java-archive',
        'application/zip',
        'text/x-python',
        'text/x-python-script',
        'application/javascript',
        'text/plain',
        'application/octet-stream'
    ];

    if (file.size > maxSize) {
        const sizeMB = Math.round(maxSize / (1024 * 1024));
        showToast(`Файл слишком большой. Максимальный размер: ${sizeMB}MB`, 'error');
        return false;
    }

    // Basic file type validation (can be bypassed, but good for UX)
    const extension = file.name.split('.').pop().toLowerCase();
    const validExtensions = CONFIG?.ALLOWED_EXTENSIONS || ['plugin', 'py'];

    if (!validExtensions.includes(extension)) {
        showToast(`Неподдерживаемый тип файла. Поддерживаются только: ${validExtensions.join(' и ')}`, 'warning');
        return false;
    }

    // Special validation for .plugin files
    if (extension === 'plugin') {
        if (!validatePluginFile(file)) {
            return false;
        }
    }

    // Special validation for .py files
    if (extension === 'py') {
        if (!validatePythonFile(file)) {
            return false;
        }
    }

    return true;
}

// Validate .plugin files
function validatePluginFile(file) {
    // Check file name pattern for .plugin files
    const fileName = file.name;

    // Basic validation - .plugin files should have reasonable names
    if (fileName.length < 5 || fileName.length > 100) {
        showToast('Некорректное имя файла плагина', 'warning');
        return false;
    }

    // Check for suspicious characters
    const suspiciousChars = /[<>:"|?*\x00-\x1f]/;
    if (suspiciousChars.test(fileName)) {
        showToast('Имя файла содержит недопустимые символы', 'warning');
        return false;
    }

    return true;
}

// Validate .py files
function validatePythonFile(file) {
    const fileName = file.name;

    // Basic validation for Python files
    if (fileName.length < 4 || fileName.length > 100) {
        showToast('Некорректное имя Python файла', 'warning');
        return false;
    }

    // Check for suspicious characters
    const suspiciousChars = /[<>:"|?*\x00-\x1f]/;
    if (suspiciousChars.test(fileName)) {
        showToast('Имя файла содержит недопустимые символы', 'warning');
        return false;
    }

    return true;
}

// Progress Animation for File Upload
function animateProgress(duration = 3000) {
    const progressFill = document.querySelector('.progress-fill');
    if (!progressFill) return;

    let start = 0;
    const increment = 100 / (duration / 50);

    const timer = setInterval(() => {
        start += increment;
        if (start >= 100) {
            start = 100;
            clearInterval(timer);
        }
        progressFill.style.width = `${start}%`;
    }, 50);

    return timer;
}

// Enhanced Upload Function
async function uploadFileEnhanced(file) {
    if (!authToken) {
        showToast('Для загрузки файлов необходима авторизация', 'warning');
        return;
    }

    if (!validateFile(file)) {
        return;
    }

    const formData = new FormData();
    formData.append('file', file);

    elements.scanProgress.classList.remove('hidden');
    elements.scanResult.classList.add('hidden');

    const progressTimer = animateProgress();

    try {
        const response = await fetch(`${API_BASE_URL}/scan/upload`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`
            },
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        displayScanResult(result.data);
        showToast('Файл успешно загружен и обрабатывается', 'success');
    } catch (error) {
        showToast('Ошибка загрузки файла: ' + error.message, 'error');
    } finally {
        clearInterval(progressTimer);
        elements.scanProgress.classList.add('hidden');
    }
}

// Keyboard Shortcuts
function initKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K for search focus
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            elements.hashInput?.focus();
        }

        // Escape to close modals/overlays
        if (e.key === 'Escape') {
            hideLoading();
        }
    });
}

// Local Storage Management
function saveToLocalStorage(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
        console.error('Failed to save to localStorage:', error);
    }
}

function loadFromLocalStorage(key, defaultValue = null) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.error('Failed to load from localStorage:', error);
        return defaultValue;
    }
}

// Search History
function saveSearchHistory(query, type) {
    const history = loadFromLocalStorage('searchHistory', []);
    const newEntry = {
        query,
        type,
        timestamp: new Date().toISOString()
    };

    // Remove duplicates and limit to 10 entries
    const filteredHistory = history.filter(item =>
        !(item.query === query && item.type === type)
    );

    filteredHistory.unshift(newEntry);
    saveToLocalStorage('searchHistory', filteredHistory.slice(0, 10));
}

// Enhanced Error Handling
function handleApiError(error, context = '') {
    console.error(`API Error ${context}:`, error);

    if (error.message.includes('401')) {
        showToast('Сессия истекла. Пожалуйста, войдите снова', 'warning');
        logout();
    } else if (error.message.includes('403')) {
        showToast('Недостаточно прав доступа', 'error');
    } else if (error.message.includes('404')) {
        showToast('Данные не найдены', 'warning');
    } else if (error.message.includes('429')) {
        showToast('Слишком много запросов. Попробуйте позже', 'warning');
    } else if (error.message.includes('500')) {
        showToast('Ошибка сервера. Попробуйте позже', 'error');
    } else {
        showToast(`Произошла ошибка: ${error.message}`, 'error');
    }
}

// Update existing functions to use enhanced error handling
const originalMakeRequest = makeRequest;
async function makeRequest(endpoint, options = {}) {
    try {
        return await originalMakeRequest(endpoint, options);
    } catch (error) {
        handleApiError(error, endpoint);
        throw error;
    }
}

// Initialize enhanced features
function initEnhancedFeatures() {
    initKeyboardShortcuts();

    // Add search history to inputs
    if (elements.queryInput) {
        elements.queryInput.addEventListener('input', (e) => {
            debouncedSearch(e.target.value);
        });
    }

    // Replace original upload function
    if (elements.fileInput) {
        elements.fileInput.removeEventListener('change', uploadFile);
        elements.fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                uploadFileEnhanced(e.target.files[0]);
            }
        });
    }

    // Update drag & drop to use enhanced upload
    if (elements.uploadArea) {
        const originalDropHandler = elements.uploadArea.ondrop;
        elements.uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            elements.uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                uploadFileEnhanced(files[0]);
            }
        });
    }
}

// Update initialization
const originalInit = init;
function init() {
    originalInit();
    initEnhancedFeatures();
}

// Global functions for HTML onclick handlers
window.scrollToSection = scrollToSection;
